"""Shared utilities for trading CLI"""
import pandas as pd
import numpy as np
import click
from typing import Tuple, Optional
from pathlib import Path
import time


def calculate_volatility(data: pd.DataFrame) -> float:
    """Calculate daily volatility"""
    returns = np.log(data['Close'] / data['Close'].shift(1))
    return returns.std() * np.sqrt(252)  # Annualized volatility


def calculate_moving_averages(data: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
    """Calculate 20 and 50 day moving averages"""
    ma20 = data['Close'].rolling(window=20).mean()
    ma50 = data['Close'].rolling(window=50).mean()
    return ma20, ma50


def calculate_rsi(data: pd.DataFrame, periods: int = 14) -> pd.Series:
    """Calculate Relative Strength Index"""
    delta = data['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))


def format_currency(value: float, currency: str = "USD", decimal_places: int = 2) -> str:
    """Format currency value for display"""
    if currency == "USD":
        return f"${value:,.{decimal_places}f}"
    else:
        return f"{value:,.{decimal_places}f} {currency}"


def format_percentage(value: float, decimal_places: int = 2, show_sign: bool = True) -> str:
    """Format percentage value for display"""
    if show_sign:
        return f"{value:+.{decimal_places}f}%"
    else:
        return f"{value:.{decimal_places}f}%"


def format_large_number(value: float, decimal_places: int = 1) -> str:
    """Format large numbers with K, M, B suffixes"""
    if abs(value) >= 1e9:
        return f"{value/1e9:.{decimal_places}f}B"
    elif abs(value) >= 1e6:
        return f"{value/1e6:.{decimal_places}f}M"
    elif abs(value) >= 1e3:
        return f"{value/1e3:.{decimal_places}f}K"
    else:
        return f"{value:.{decimal_places}f}"


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """Safely divide two numbers, returning default if denominator is zero"""
    return numerator / denominator if denominator != 0 else default


def validate_date_format(date_str: str) -> bool:
    """Validate date string is in YYYY-MM-DD format"""
    try:
        pd.to_datetime(date_str, format='%Y-%m-%d')
        return True
    except (ValueError, TypeError):
        return False


def validate_symbol(symbol: str) -> bool:
    """Basic validation for stock symbols"""
    if not symbol or not isinstance(symbol, str):
        return False
    
    # Remove whitespace and convert to uppercase
    symbol = symbol.strip().upper()
    
    # Basic checks: 1-5 characters, alphanumeric
    if len(symbol) < 1 or len(symbol) > 5:
        return False
    
    return symbol.isalnum()


def create_directory_if_not_exists(path: Path) -> bool:
    """Create directory if it doesn't exist"""
    try:
        path.mkdir(parents=True, exist_ok=True)
        return True
    except OSError as e:
        click.echo(f"Error creating directory {path}: {e}")
        return False


def rate_limit_delay(delay_seconds: float, current_request: int, total_requests: int):
    """Apply rate limiting delay with user feedback"""
    if current_request > 0 and delay_seconds > 0:
        if total_requests > 1:
            click.echo(f"⏳ Waiting {delay_seconds}s before next request "
                      f"({current_request + 1}/{total_requests})...")
        time.sleep(delay_seconds)


def display_progress(current: int, total: int, item_name: str = "item"):
    """Display progress information"""
    if total > 1:
        percentage = (current / total) * 100
        click.echo(f"📊 Processing {item_name} {current}/{total} ({percentage:.1f}%)")


def handle_keyboard_interrupt(func):
    """Decorator to handle keyboard interrupts gracefully"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except KeyboardInterrupt:
            click.echo("\n⚠️  Operation cancelled by user")
            return None
    return wrapper


def validate_file_exists(filepath: str) -> bool:
    """Validate that a file exists"""
    return Path(filepath).exists()


def get_file_size_mb(filepath: str) -> float:
    """Get file size in megabytes"""
    try:
        return Path(filepath).stat().st_size / (1024 * 1024)
    except OSError:
        return 0.0


def truncate_string(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """Truncate string to maximum length"""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def parse_symbol_list(symbols_input: Optional[str]) -> list:
    """Parse comma-separated symbol list"""
    if not symbols_input:
        return []
    
    symbols = []
    for symbol in symbols_input.split(','):
        symbol = symbol.strip().upper()
        if validate_symbol(symbol):
            symbols.append(symbol)
        else:
            click.echo(f"⚠️  Invalid symbol: {symbol}")
    
    return symbols


def load_symbols_from_file(filepath: str) -> list:
    """Load symbols from JSON file"""
    try:
        import json
        with open(filepath, 'r') as f:
            data = json.load(f)
            
        if isinstance(data, list):
            return [s.upper() for s in data if validate_symbol(s)]
        else:
            click.echo(f"⚠️  JSON file must contain a list of symbols")
            return []
            
    except (json.JSONDecodeError, IOError) as e:
        click.echo(f"⚠️  Error loading symbols file: {e}")
        return []


class ProgressBar:
    """Simple progress bar for long operations"""
    
    def __init__(self, total: int, description: str = "Processing"):
        self.total = total
        self.current = 0
        self.description = description
    
    def update(self, increment: int = 1):
        """Update progress"""
        self.current += increment
        percentage = (self.current / self.total) * 100
        bar_length = 30
        filled_length = int(bar_length * self.current // self.total)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        
        click.echo(f'\r{self.description}: |{bar}| {percentage:.1f}% ({self.current}/{self.total})', nl=False)
        
        if self.current >= self.total:
            click.echo()  # New line when complete
    
    def finish(self):
        """Mark progress as complete"""
        if self.current < self.total:
            self.current = self.total
            self.update(0)
