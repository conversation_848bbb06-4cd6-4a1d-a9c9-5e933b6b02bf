"""Configuration management for trading CLI"""
import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import click


class ConfigManager:
    """Manages configuration settings for the trading CLI"""
    
    DEFAULT_CONFIG = {
        "data": {
            "default_days": 30,
            "delay_seconds": 2.0,
            "use_prod": False,
            "output_dir": None,
            "data_sources": ["yahoo"]
        },
        "analysis": {
            "default_format": "table",
            "save_results": True,
            "technical_indicators": ["rsi", "ma20", "ma50", "volatility"]
        },
        "ai": {
            "default_model": "gpt-4",
            "save_analysis": True,
            "temperature": 0.3,
            "max_tokens": 2000
        },
        "display": {
            "color": True,
            "verbose": False,
            "quiet": False,
            "decimal_places": 2
        },
        "portfolio": {
            "default_currency": "USD",
            "track_dividends": True,
            "rebalance_threshold": 0.05
        },
        "alerts": {
            "check_interval": 300,  # 5 minutes
            "notification_methods": ["console"]
        }
    }
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize configuration manager
        
        Args:
            config_path: Path to config file. If None, uses default location.
        """
        if config_path:
            self.config_path = Path(config_path)
        else:
            # Default config location
            home_dir = Path.home()
            self.config_path = home_dir / ".trading-cli" / "config.json"
        
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default"""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                # Merge with defaults to ensure all keys exist
                return self._merge_configs(self.DEFAULT_CONFIG, config)
            except (json.JSONDecodeError, IOError) as e:
                click.echo(f"Warning: Error loading config file: {e}")
                click.echo("Using default configuration.")
                return self.DEFAULT_CONFIG.copy()
        else:
            return self.DEFAULT_CONFIG.copy()
    
    def save_config(self) -> bool:
        """Save current configuration to file"""
        try:
            # Create config directory if it doesn't exist
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
            return True
        except IOError as e:
            click.echo(f"Error saving config file: {e}")
            return False
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation
        
        Args:
            key_path: Dot-separated path to config value (e.g., 'data.default_days')
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value: Any) -> bool:
        """Set configuration value using dot notation
        
        Args:
            key_path: Dot-separated path to config value
            value: Value to set
            
        Returns:
            True if successful, False otherwise
        """
        keys = key_path.split('.')
        config = self.config
        
        # Navigate to parent of target key
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # Set the value
        config[keys[-1]] = value
        return self.save_config()
    
    def reset(self) -> bool:
        """Reset configuration to defaults"""
        self.config = self.DEFAULT_CONFIG.copy()
        return self.save_config()
    
    def _merge_configs(self, default: Dict, user: Dict) -> Dict:
        """Recursively merge user config with defaults"""
        result = default.copy()
        
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get_data_directory(self, base_path: str, prod: Optional[bool] = None, 
                          output_dir: Optional[str] = None) -> Path:
        """Get appropriate data directory based on configuration and parameters
        
        Args:
            base_path: Base path like 'yahoo', 'ai_analysis', etc.
            prod: Override prod setting from config
            output_dir: Custom output directory (overrides prod)
            
        Returns:
            Path object for the data directory
        """
        if output_dir:
            return Path(f'{output_dir}/{base_path}')
        
        use_prod = prod if prod is not None else self.get('data.use_prod', False)
        
        if use_prod:
            return Path(f'data.prod/{base_path}')
        else:
            return Path(f'data/{base_path}')


# Global config instance
_config_manager = None

def get_config() -> ConfigManager:
    """Get global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager
