import click
from pathlib import Path
import os
from .core.config import get_config
from .commands.config_commands import config
from .commands.data_commands import data
from .commands.analysis_commands import analysis
from .commands.ai_commands import ai
from .commands.sector_commands import sector
from .commands.portfolio_commands import portfolio
from .commands.watchlist_commands import watchlist
from .commands.alerts_commands import alerts
from .commands.market_commands import market


# Global options that can be used by all commands
@click.group()
@click.option('--config-file', help='Path to configuration file')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.option('--quiet', '-q', is_flag=True, help='Suppress non-essential output')
@click.version_option()
@click.pass_context
def cli(ctx, config_file, verbose, quiet):
    """Trading CLI tool for managing and analyzing trades"""
    # Ensure context object exists
    ctx.ensure_object(dict)

    # Store global options in context
    ctx.obj['verbose'] = verbose
    ctx.obj['quiet'] = quiet
    ctx.obj['config_file'] = config_file

    # Initialize configuration
    if config_file:
        # TODO: Initialize config with custom file path
        pass


# Add command groups
cli.add_command(config)
cli.add_command(data)
cli.add_command(analysis)
cli.add_command(ai)
cli.add_command(sector)
cli.add_command(portfolio)
cli.add_command(watchlist)
cli.add_command(alerts)
cli.add_command(market)


# Legacy commands for backward compatibility will be added here as needed


if __name__ == '__main__':
    cli()
