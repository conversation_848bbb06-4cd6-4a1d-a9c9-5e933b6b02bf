"""Price alerts and notifications commands"""
import click


@click.group()
def alerts():
    """Price alerts and notifications commands"""
    pass


@alerts.command()
def create():
    """Create alert (placeholder)"""
    click.echo("Alerts create command - to be implemented")


@alerts.command()
def list():
    """List alerts (placeholder)"""
    click.echo("<PERSON><PERSON>s list command - to be implemented")


@alerts.command()
def remove():
    """Remove alert (placeholder)"""
    click.echo("<PERSON>erts remove command - to be implemented")


@alerts.command()
def check():
    """Check alerts (placeholder)"""
    click.echo("<PERSON><PERSON>s check command - to be implemented")
