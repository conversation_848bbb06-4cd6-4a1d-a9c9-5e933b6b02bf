"""Configuration management commands"""
import click
import json
from ..core.config import get_config


@click.group()
def config():
    """Configuration management commands"""
    pass


@config.command()
def init():
    """Initialize configuration file with defaults"""
    config_manager = get_config()
    
    if config_manager.config_path.exists():
        if not click.confirm(f"Configuration file already exists at {config_manager.config_path}. Overwrite?"):
            click.echo("Configuration initialization cancelled.")
            return
    
    if config_manager.save_config():
        click.echo(f"✅ Configuration initialized at: {config_manager.config_path}")
        click.echo("Use 'trading-cli config show' to view current settings.")
    else:
        click.echo("❌ Failed to initialize configuration file.")


@config.command()
def show():
    """Show current configuration"""
    config_manager = get_config()
    
    click.echo(f"📁 Configuration file: {config_manager.config_path}")
    click.echo(f"📄 File exists: {'Yes' if config_manager.config_path.exists() else 'No'}")
    click.echo("\n" + "=" * 50)
    click.echo("CURRENT CONFIGURATION")
    click.echo("=" * 50)
    
    # Pretty print the configuration
    config_json = json.dumps(config_manager.config, indent=2)
    click.echo(config_json)


@config.command()
@click.argument('key')
@click.argument('value')
def set(key, value):
    """Set a configuration value
    
    Examples:
        trading-cli config set data.default_days 60
        trading-cli config set ai.default_model gpt-3.5-turbo
        trading-cli config set display.color false
    """
    config_manager = get_config()
    
    # Try to parse value as appropriate type
    parsed_value = _parse_config_value(value)
    
    if config_manager.set(key, parsed_value):
        click.echo(f"✅ Set {key} = {parsed_value}")
    else:
        click.echo(f"❌ Failed to set configuration value")


@config.command()
@click.argument('key')
def get(key):
    """Get a configuration value
    
    Examples:
        trading-cli config get data.default_days
        trading-cli config get ai.default_model
    """
    config_manager = get_config()
    value = config_manager.get(key)
    
    if value is not None:
        click.echo(f"{key} = {value}")
    else:
        click.echo(f"❌ Configuration key '{key}' not found")


@config.command()
def reset():
    """Reset configuration to defaults"""
    if not click.confirm("This will reset all configuration to defaults. Continue?"):
        click.echo("Reset cancelled.")
        return
    
    config_manager = get_config()
    
    if config_manager.reset():
        click.echo("✅ Configuration reset to defaults")
    else:
        click.echo("❌ Failed to reset configuration")


@config.command()
def path():
    """Show configuration file path"""
    config_manager = get_config()
    click.echo(f"Configuration file: {config_manager.config_path}")


def _parse_config_value(value_str: str):
    """Parse string value to appropriate Python type"""
    # Try boolean
    if value_str.lower() in ('true', 'false'):
        return value_str.lower() == 'true'
    
    # Try integer
    try:
        return int(value_str)
    except ValueError:
        pass
    
    # Try float
    try:
        return float(value_str)
    except ValueError:
        pass
    
    # Try JSON (for lists, dicts)
    try:
        return json.loads(value_str)
    except json.JSONDecodeError:
        pass
    
    # Return as string
    return value_str
