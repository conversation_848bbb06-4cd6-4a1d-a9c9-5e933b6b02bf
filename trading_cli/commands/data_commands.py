"""Data management commands"""
import click
import yfinance as yf
from datetime import datetime, timedelta
from pathlib import Path
from ..core.config import get_config
from ..core.utils import (
    validate_symbol,
    load_symbols_from_file,
    rate_limit_delay,
    display_progress,
    create_directory_if_not_exists
)


@click.group()
def data():
    """Data management commands"""
    pass


@data.command()
@click.argument('symbol', required=False)
@click.option('--file', '-f', type=click.Path(exists=True),
              help='JSON file containing list of symbols')
@click.option('--start', '-s', help='Start date (YYYY-MM-DD)')
@click.option('--end', '-e', help='End date (YYYY-MM-DD)')
@click.option('--days', '-d', type=int,
              help='Number of days of history (overrides config default)')
@click.option('--prod', is_flag=True,
              help='Save data to data.prod directory instead of data')
@click.option('--output-dir', '-o',
              help='Custom output directory (overrides --prod)')
@click.option('--delay', type=float,
              help='Delay between requests in seconds (overrides config default)')
@click.option('--source', default='yahoo',
              help='Data source (default: yahoo)')
def fetch(symbol, file, start, end, days, prod, output_dir, delay, source):
    """Fetch historical market data for given symbol(s)

    Examples:
        trading-cli data fetch AAPL
        trading-cli data fetch --file data/symbols.json --days 90
        trading-cli data fetch MSFT --start 2024-01-01 --end 2024-12-31
    """
    config = get_config()

    # Collect symbols
    symbols = []
    if symbol:
        if validate_symbol(symbol):
            symbols.append(symbol.upper())
        else:
            click.echo(f"❌ Invalid symbol: {symbol}")
            return

    if file:
        file_symbols = load_symbols_from_file(file)
        symbols.extend(file_symbols)

    if not symbols:
        click.echo("❌ Please provide either a symbol or a file containing symbols")
        return

    # Get configuration values with overrides
    days = days or config.get('data.default_days', 30)
    delay = delay if delay is not None else config.get('data.delay_seconds', 2.0)

    # Calculate date range
    if not start:
        end_date = datetime.now() if not end else datetime.strptime(end, '%Y-%m-%d')
        start_date = end_date - timedelta(days=days)
        start = start_date.strftime('%Y-%m-%d')
        end = end_date.strftime('%Y-%m-%d')

    # Get data directory
    data_dir = config.get_data_directory(source, prod, output_dir)
    if not create_directory_if_not_exists(data_dir):
        return

    # Display information
    if output_dir:
        click.echo(f"📁 Using custom output directory: {data_dir}")
    elif prod:
        click.echo(f"📁 Using production data directory: {data_dir}")

    click.echo(f"📊 Fetching data for {len(symbols)} symbol(s) from {start} to {end}")

    if len(symbols) > 1:
        click.echo(f"⏳ Using {delay}s delay between requests to avoid rate limiting")

    # Fetch data for each symbol
    successful_fetches = 0
    for i, sym in enumerate(symbols):
        rate_limit_delay(delay, i, len(symbols))
        display_progress(i + 1, len(symbols), f"symbol {sym}")

        try:
            # Fetch data based on source
            if source == 'yahoo':
                success = _fetch_yahoo_data(sym, start, end, data_dir)
            else:
                click.echo(f"❌ Unsupported data source: {source}")
                continue

            if success:
                successful_fetches += 1

        except Exception as e:
            click.echo(f"❌ Error fetching {sym}: {str(e)}")

    # Summary
    click.echo(f"\n✅ Successfully fetched data for {successful_fetches}/{len(symbols)} symbols")
    if successful_fetches < len(symbols):
        click.echo(f"⚠️  {len(symbols) - successful_fetches} symbols failed")


def _fetch_yahoo_data(symbol: str, start: str, end: str, data_dir: Path) -> bool:
    """Fetch data from Yahoo Finance"""
    try:
        ticker = yf.Ticker(symbol)
        hist = ticker.history(start=start, end=end)

        if hist.empty:
            click.echo(f"❌ No data found for symbol {symbol}")
            return False

        # Create filename
        filename = f"{symbol}_{start}_{end}.csv"
        filename = filename.replace('/', '-').upper()
        filepath = data_dir / filename

        # Write to file
        with open(filepath, 'w') as f:
            f.write("Date,Open,High,Low,Close,Volume\n")
            for date, row in hist.iterrows():
                f.write(f"{date.strftime('%Y-%m-%d')},"
                       f"{row['Open']:.2f},"
                       f"{row['High']:.2f},"
                       f"{row['Low']:.2f},"
                       f"{row['Close']:.2f},"
                       f"{int(row['Volume'])}\n")

        click.echo(f"✅ {symbol}: Data saved to {filepath}")
        return True

    except Exception as e:
        click.echo(f"❌ {symbol}: {str(e)}")
        return False


@data.command('ls')
@click.option('--source', default='all', help='Data source to list (default: all)')
@click.option('--prod', is_flag=True, help='List production data')
@click.option('--output-dir', '-o', help='Custom output directory')
def ls(source, prod, output_dir):
    """List available data files"""
    config = get_config()

    if source == 'all':
        sources = ['yahoo', 'ai_analysis', 'sector_analysis']
    else:
        sources = [source]

    total_files = 0
    total_size = 0

    for src in sources:
        data_dir = config.get_data_directory(src, prod, output_dir)

        if not data_dir.exists():
            click.echo(f"📁 {src}: No data directory found at {data_dir}")
            continue

        files = list(data_dir.glob('*'))
        if not files:
            click.echo(f"📁 {src}: No files found in {data_dir}")
            continue

        click.echo(f"\n📁 {src.upper()} ({data_dir}):")
        click.echo("─" * 60)

        for file_path in sorted(files):
            if file_path.is_file():
                size_mb = file_path.stat().st_size / (1024 * 1024)
                modified = datetime.fromtimestamp(file_path.stat().st_mtime)
                click.echo(f"  {file_path.name:<40} {size_mb:>6.2f}MB  {modified.strftime('%Y-%m-%d %H:%M')}")
                total_files += 1
                total_size += size_mb

    if total_files > 0:
        click.echo(f"\n📊 Total: {total_files} files, {total_size:.2f}MB")
    else:
        click.echo("📭 No data files found")


@data.command()
@click.option('--source', default='all', help='Data source to clean (default: all)')
@click.option('--older-than', type=int, default=30,
              help='Remove files older than N days (default: 30)')
@click.option('--prod', is_flag=True, help='Clean production data')
@click.option('--output-dir', '-o', help='Custom output directory')
@click.option('--dry-run', is_flag=True, help='Show what would be deleted without deleting')
def clean(source, older_than, prod, output_dir, dry_run):
    """Clean old data files"""
    config = get_config()

    if source == 'all':
        sources = ['yahoo', 'ai_analysis', 'sector_analysis']
    else:
        sources = [source]

    cutoff_date = datetime.now() - timedelta(days=older_than)
    total_files = 0
    total_size = 0

    for src in sources:
        data_dir = config.get_data_directory(src, prod, output_dir)

        if not data_dir.exists():
            continue

        files_to_delete = []
        for file_path in data_dir.glob('*'):
            if file_path.is_file():
                modified = datetime.fromtimestamp(file_path.stat().st_mtime)
                if modified < cutoff_date:
                    files_to_delete.append(file_path)

        if files_to_delete:
            click.echo(f"\n📁 {src.upper()} ({data_dir}):")
            click.echo("─" * 60)

            for file_path in files_to_delete:
                size_mb = file_path.stat().st_size / (1024 * 1024)
                modified = datetime.fromtimestamp(file_path.stat().st_mtime)

                if dry_run:
                    click.echo(f"  Would delete: {file_path.name} ({size_mb:.2f}MB, {modified.strftime('%Y-%m-%d')})")
                else:
                    try:
                        file_path.unlink()
                        click.echo(f"  ✅ Deleted: {file_path.name} ({size_mb:.2f}MB)")
                    except Exception as e:
                        click.echo(f"  ❌ Failed to delete {file_path.name}: {e}")

                total_files += 1
                total_size += size_mb

    if total_files > 0:
        action = "Would delete" if dry_run else "Deleted"
        click.echo(f"\n📊 {action}: {total_files} files, {total_size:.2f}MB")
        if dry_run:
            click.echo("Use --no-dry-run to actually delete the files")
    else:
        click.echo(f"📭 No files older than {older_than} days found")


@data.command()
def export():
    """Export data in different formats (placeholder)"""
    click.echo("📤 Data export command - to be implemented")
    click.echo("Future formats: JSON, Parquet, Excel, etc.")
